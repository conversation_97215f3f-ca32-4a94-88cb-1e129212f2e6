import React, { useState, useRef, useEffect } from 'react';
import { CaretDown } from '@phosphor-icons/react';

interface DurationSelectorProps {
  value: number; // Duration in minutes
  onChange: (duration: number) => void;
  className?: string;
}

const DurationSelector: React.FC<DurationSelectorProps> = ({
  value,
  onChange,
  className,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectorRef = useRef<HTMLDivElement>(null);
  const [selectedValue, setSelectedValue] = useState(value);

  // Available durations in minutes
  const durations = [15, 45, 50, 60];

  // Detect mobile devices
  const isMobile = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
           window.innerWidth < 768;
  };

  // Update internal state when prop changes
  useEffect(() => {
    // Validate the value to ensure it's within a reasonable range
    const validatedValue = validateDuration(value);
    setSelectedValue(validatedValue);
  }, [value]);

  // Validate duration to ensure it's within a reasonable range
  const validateDuration = (minutes: number): number => {
    // If the duration is less than 15 minutes, default to 15 minutes
    if (minutes < 15) {
      console.warn(`Duration ${minutes} minutes is too short, defaulting to 15 minutes`);
      return 15;
    }

    // If the duration is more than 4 hours (240 minutes), cap it at 4 hours
    if (minutes > 240) {
      console.warn(`Duration ${minutes} minutes is too long, capping at 4 hours (240 minutes)`);
      return 240;
    }

    // If the duration is not in the predefined list, find the closest value
    if (!durations.includes(minutes)) {
      // Find the closest duration in the predefined list
      const closestDuration = durations.reduce((prev, curr) => {
        return Math.abs(curr - minutes) < Math.abs(prev - minutes) ? curr : prev;
      });

      console.warn(`Duration ${minutes} minutes is not in the predefined list, using closest value: ${closestDuration} minutes`);
      return closestDuration;
    }

    return minutes;
  };

  // Format duration for display
  const formatDuration = (minutes: number): string => {
    // For durations less than 60 minutes, display in minutes
    if (minutes < 60) {
      return `${minutes} minutes`;
    }

    // For durations of 60 minutes or more, display in hours and minutes
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      // If it's an exact number of hours
      return hours === 1 ? `1 hour` : `${hours} hours`;
    } else {
      // If there are remaining minutes
      const hourText = hours === 1 ? `1 hour` : `${hours} hours`;
      return `${hourText} ${remainingMinutes} min`;
    }
  };

  const handleSelectDuration = (duration: number) => {
    // Validate the duration
    const validatedDuration = validateDuration(duration);

    // Update internal state
    setSelectedValue(validatedDuration);

    // Call the parent's onChange handler
    onChange(validatedDuration);

    // Close the dropdown immediately for better UX
    setShowDropdown(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Prevent background scroll on mobile when dropdown is open
  useEffect(() => {
    if (isMobile() && showDropdown) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [showDropdown]);

  const handleToggleDropdown = () => {
    setShowDropdown(!showDropdown);
  };

  return (
    <>
      {/* Mobile overlay to prevent background interaction */}
      {isMobile() && showDropdown && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-[9998]"
          onClick={() => setShowDropdown(false)}
        />
      )}

      <div className={`relative min-w-[120px] ${className}`} ref={dropdownRef}>
        <div
          ref={selectorRef}
          className="flex items-center justify-between w-full text-sm py-3 px-2.5 border rounded-lg outline-none cursor-pointer text-primary border-[#D9D9D9]"
          onClick={handleToggleDropdown}
          style={{ height: '42px' }} // Ensure consistent height with time pickers
        >
          <span>{formatDuration(selectedValue)}</span>
          <CaretDown size={16} className="text-gray-500" />
        </div>

      <div
        className={`absolute mt-1 w-full max-h-32 overflow-y-auto z-10 bg-white shadow-[0px_4px_12px_0px_#2C58BB1A] rounded-lg transition-all duration-200 ${
          showDropdown
            ? "visible opacity-100 translate-y-0"
            : "invisible opacity-0 -translate-y-5"
        }`}
        style={{
          // Prevent background scroll on mobile when dropdown is open
          ...(isMobile() && showDropdown && {
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '90%',
            maxWidth: '300px',
            zIndex: 9999,
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
          })
        }}
      >
        {durations.map((durationOption) => (
          <div
            key={durationOption}
            onClick={() => handleSelectDuration(durationOption)}
            className={`px-4 py-2 cursor-pointer hover:bg-gray-100/20 transition duration-200 text-sm ${
              selectedValue === durationOption
                ? "bg-blue-600/10 text-blue-600 font-medium"
                : "text-primary"
            }`}
            style={{
              // Ensure proper touch targets on mobile
              ...(isMobile() && {
                minHeight: '44px',
                display: 'flex',
                alignItems: 'center'
              })
            }}
          >
            {formatDuration(durationOption)}
          </div>
        ))}
      </div>
    </div>
    </>
  );
};

export default DurationSelector;
